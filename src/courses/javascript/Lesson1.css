.lesson-container {
  font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.lesson-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  border-radius: 10px;
  margin-bottom: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.lesson-title {
  font-size: 2.5rem;
  margin: 0 0 15px 0;
  text-align: center;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.lesson-objective {
  background-color: rgba(255, 255, 255, 0.15);
  padding: 20px;
  border-radius: 8px;
  margin-top: 20px;
}

.lesson-objective h3 {
  margin: 0 0 10px 0;
  color: #ffd700;
}

.lesson-objective p {
  margin: 0;
  font-size: 1.1rem;
  line-height: 1.6;
}

.lesson-content {
  background-color: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.section {
  margin-bottom: 40px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e9ecef;
}

.section:last-child {
  border-bottom: none;
}

.section h2 {
  color: #2d3748;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #667eea;
  position: relative;
}

.section h2::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 50px;
  height: 2px;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.section h4 {
  color: #4a5568;
  margin: 20px 0 10px 0;
}

.code-block {
  background-color: #2d3748;
  color: #e2e8f0;
  border-radius: 8px;
  padding: 15px;
  font-family: 'Fira Code', 'Courier New', monospace;
  margin: 15px 0;
  overflow-x: auto;
  border-left: 4px solid #4299e1;
  line-height: 1.5;
}

.code-block code {
  font-size: 14px;
}

.tip-box {
  background-color: #ebf8ff;
  border: 1px solid #bee3f8;
  border-radius: 8px;
  padding: 15px;
  margin: 20px 0;
  border-left: 4px solid #3182ce;
  display: flex;
  align-items: flex-start;
}

.tip-box .tip-icon {
  font-size: 1.5rem;
  margin-right: 15px;
  flex-shrink: 0;
}

.tip-box .tip-content {
  flex: 1;
}

.rule-box {
  background-color: #c6f6d5;
  border: 1px solid #9ae6b4;
  border-radius: 8px;
  padding: 15px;
  margin: 15px 0;
  border-left: 4px solid #38a169;
}

.table-responsive {
  overflow-x: auto;
  margin: 20px 0;
}

.data-types-table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  overflow: hidden;
}

.data-types-table th,
.data-types-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #e2e8f0;
}

.data-types-table th {
  background-color: #4a5568;
  color: white;
  font-weight: 600;
}

.data-types-table tr:nth-child(even) {
  background-color: #f7fafc;
}

.data-types-table tr:hover {
  background-color: #edf2f7;
}

.exercise-box {
  background-color: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
  margin: 25px 0;
  border-left: 4px solid #805ad5;
}

.exercise-box h3 {
  color: #805ad5;
  margin-top: 0;
  margin-bottom: 15px;
}

.exercise-content pre {
  background-color: #2d3748;
  color: #e2e8f0;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  font-family: 'Fira Code', 'Courier New', monospace;
  margin: 10px 0;
}

code {
  background-color: #e2e8f0;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Fira Code', 'Courier New', monospace;
  font-size: 0.9em;
}

ul {
  padding-left: 20px;
}

li {
  margin-bottom: 8px;
  line-height: 1.6;
}

@media (max-width: 768px) {
  .lesson-container {
    padding: 10px;
  }
  
  .lesson-header {
    padding: 20px;
  }
  
  .lesson-title {
    font-size: 2rem;
  }
  
  .lesson-content {
    padding: 20px;
  }
  
  .code-block {
    font-size: 12px;
    padding: 10px;
  }
  
  .tip-box {
    flex-direction: column;
  }
  
  .tip-box .tip-icon {
    margin-right: 0;
    margin-bottom: 10px;
  }
}